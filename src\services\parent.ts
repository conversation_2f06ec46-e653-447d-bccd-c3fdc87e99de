// @ts-ignore
/* eslint-disable */
import { request } from '@umijs/max';

/**
 * 家长端服务
 * @description 家长端问卷填写相关的API接口
 */

/** 验证家长手机号 POST /api/parent/verify-phone */
export async function verifyParentPhone(params: API.IParentPhoneVerifyParams) {
  return request<API.ResType<API.IParentPhoneVerifyResponse>>(
    '/api/parent/verify-phone',
    {
      method: 'POST',
      data: params,
    },
  );
}

/** 获取学生的教师列表 GET /api/parent/student-teachers */
export async function getTeacherListForClassParams(
  params: API.IGetTeacherListForClassParams,
) {
  return request<API.ResType<API.IStudentTeacherInfo[]>>(
    `/api/sso/teachers/${params.enterpriseCode}`,
    {
      method: 'GET',
      params: {
        gradeCode: params.gradeCode,
        classCode: params.classCode,
      },
    },
  );
}

/** 获取学生的问卷信息 GET /api/parent/questionnaire */
export async function getQuestionnaireForParent(
  params: API.IGetQuestionnaireForParentParams,
) {
  return request<API.ResType<API.IParentQuestionnaireInfo>>(
    '/api/parent/questionnaire',
    {
      method: 'GET',
      params,
    },
  );
}

/** 获取学生的所有问卷列表 GET /api/parent/student-questionnaires */
export async function getStudentQuestionnaires(
  params: API.IGetStudentQuestionnairesParams,
) {
  return request<API.ResType<API.IStudentQuestionnairesResponse>>(
    '/api/parent/student-questionnaires',
    {
      method: 'GET',
      params,
    },
  );
}

/** 提交家长端问卷评价 POST /api/response */
export async function submitParentEvaluation(
  params: API.IParentSubmitEvaluationParams,
) {
  return request<API.ResType<API.IParentSubmitEvaluationResponse>>(
    '/api/response',
    {
      method: 'POST',
      data: params,
    },
  );
}

/** 检查学生是否已提交评价 GET /api/parent/check-submission */
export async function checkStudentSubmission(params: {
  student_id: string;
  questionnaire_id: number;
  parent_phone: string;
}) {
  return request<API.ResType<{ is_submitted: boolean; submitted_at?: Date }>>(
    '/api/parent/check-submission',
    {
      method: 'GET',
      params,
    },
  );
}

/** 获取家长已提交的评价数据 GET /api/parent/submitted-evaluation */
export async function getSubmittedEvaluation(params: {
  questionnaire_id: number;
  parent_phone: string;
  sso_student_code: string;
}) {
  return request<API.ResType<API.IEvaluationData>>(
    '/api/parent/submitted-evaluation',
    {
      method: 'GET',
      params,
    },
  );
}
