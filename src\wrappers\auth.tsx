/*
 * @description: 统一页面鉴权
 * @author: zp<PERSON>, AI Assistant
 * @Date: 2021-08-19 11:59:39
 * @LastEditTime: 2025-06-20 09:07:01
 */
import AuthHandler from '@/components/AuthHandler';
import NotFind from '@/pages/403';
import { isAuthenticated } from '@/utils/auth';
import { getQueryObj } from '@/utils/env';
import { Access, Outlet, useAccess } from '@umijs/max';

export default () => {
  const { isLogin } = useAccess();
  const { value } = getQueryObj();

  // 如果URL中有value参数或者已经认证，使用AuthHandler处理认证
  if (value || isAuthenticated()) {
    return (
      <AuthHandler>
        <Access accessible={isLogin} fallback={<NotFind />}>
          <Outlet />
        </Access>
      </AuthHandler>
    );
  }

  // 如果没有认证，显示未授权页面
  return <NotFind />;
};
