import type { ISchoolStatistics } from '@/types/statistics';
import {
  PercentageOutlined,
  TrophyOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { Card, Col, Row, Skeleton, Statistic } from 'antd';
import React from 'react';

interface SchoolOverviewProps {
  data: ISchoolStatistics | null;
  loading?: boolean;
}

/**
 * 学校整体统计卡片组件
 */
const SchoolOverview: React.FC<SchoolOverviewProps> = ({
  data,
  loading = false,
}) => {
  if (loading) {
    return (
      <Row gutter={16} style={{ marginBottom: 16 }}>
        {[1, 2, 3, 4].map((item) => (
          <Col span={6} key={item}>
            <Card>
              <Skeleton active paragraph={{ rows: 2 }} />
            </Card>
          </Col>
        ))}
      </Row>
    );
  }

  if (!data) {
    return (
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={24}>
          <Card>
            <div
              style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}
            >
              暂无统计数据
            </div>
          </Card>
        </Col>
      </Row>
    );
  }

  console.log(data);
  return (
    <Row gutter={16} style={{ marginBottom: 16 }}>
      <Col span={6}>
        <Card>
          <Statistic
            title={
              <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                <TrophyOutlined style={{ color: '#faad14', fontSize: 24 }} />
                学校平均分
              </div>
            }
            value={(data.school_average_score || '--') + '分'}
            precision={1}
            valueStyle={{ color: '#faad14' }}
          />
          <div
            style={{
              marginTop: 8,
              fontSize: '12px',
              color: '#999',
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
            }}
          >
            {data.sso_school_name || '学校'}
          </div>
        </Card>
      </Col>

      <Col span={6}>
        <Card>
          <Statistic
            title={
              <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                <UserOutlined style={{ color: '#52c41a', fontSize: 24 }} />
                参与人数
              </div>
            }
            value={(data.completed_responses || '--') + '人'}
            valueStyle={{ color: '#52c41a' }}
          />
          <div
            style={{
              marginTop: 8,
              fontSize: '12px',
              color: '#999',
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
            }}
          >
            总响应数：{data.total_responses}
          </div>
        </Card>
      </Col>

      <Col span={6}>
        <Card>
          <Statistic
            title={
              <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                <PercentageOutlined
                  style={{ color: '#1890ff', fontSize: 24 }}
                />
                完成率
              </div>
            }
            value={(data.completion_rate || '--') + '%'}
            precision={1}
            valueStyle={{ color: '#1890ff' }}
          />
          <div
            style={{
              marginTop: 8,
              fontSize: '12px',
              color: '#999',
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
            }}
          >
            已完成/总数
          </div>
        </Card>
      </Col>

      <Col span={6}>
        <Card>
          <Statistic
            title={
              <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                <UserOutlined style={{ color: '#722ed1', fontSize: 24 }} />
                被评教师数
              </div>
            }
            value={(data.total_teachers_evaluated || '--') + '人'}
            valueStyle={{ color: '#722ed1' }}
          />
          <div
            style={{
              marginTop: 8,
              fontSize: '12px',
              color: '#999',
              whiteSpace: 'nowrap',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
            }}
          >
            平均分：{data.teacher_average_score?.toFixed(1) || 0}分
          </div>
        </Card>
      </Col>
    </Row>
  );
};

export default SchoolOverview;
