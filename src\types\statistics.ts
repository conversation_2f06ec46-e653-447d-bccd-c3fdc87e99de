/**
 * 统计分析接口数据结构定义
 * @description 为后台实现统计接口提供完整的数据结构定义
 */

// ==================== 基础查询参数 ====================

/**
 * 统计查询参数接口
 * @description 所有统计接口的通用查询参数
 */
export interface IStatisticsQuery {
  /** SSO学校代码，可选 */
  sso_school_code?: string;
  /** SSO教师ID，可选 */
  sso_teacher_id?: string;
  /** 月份，格式：YYYY-MM，可选 */
  month?: string;
  /** 开始时间，格式：YYYY-MM-DD，可选 */
  start_time?: string;
  /** 结束时间，格式：YYYY-MM-DD，可选 */
  end_time?: string;
  /** 页码，默认1，可选 */
  page?: number;
  /** 每页数量，默认10，可选 */
  limit?: number;
}

// ==================== 学校统计相关 ====================

/**
 * 学校统计数据接口
 * @description 学校维度的统计分析数据
 * @api GET /api/statistics/school
 */
export interface ISchoolStatistics {
  /** SSO学校代码 */
  sso_school_code: string;
  /** SSO学校名称 */
  sso_school_name: string;
  /** 统计月份，格式：YYYY-MM */
  month: string;
  /** 总响应数 */
  total_responses: number;
  /** 已完成响应数 */
  completed_responses: number;
  /** 完成率（百分比，0-100） */
  completion_rate: number;
  /** 学校平均分（百分制，0-100） */
  school_average_score: number;
  /** 教师平均分（百分制，0-100） */
  teacher_average_score: number;
  /** 被评价教师总数 */
  total_teachers_evaluated: number;
}

// ==================== 教师排名相关 ====================

/**
 * 教师排名数据接口
 * @description 单个教师的排名信息
 */
export interface ITeacherRanking {
  /** SSO教师ID */
  sso_teacher_id: string;
  /** SSO教师姓名 */
  sso_teacher_name: string;
  /** SSO教师科目 */
  sso_teacher_subject: string;
  /** SSO教师部门 */
  sso_teacher_department: string;
  /** 平均分（百分制，0-100） */
  average_score: number;
  /** 评价数量 */
  evaluation_count: number;
  /** 推荐率（百分比，0-100） */
  recommendation_rate: number;
  /** 排名 */
  rank: number;
}

/**
 * 教师排名响应接口
 * @description 教师排名查询的响应数据结构
 * @api GET /api/statistics/teacher-ranking
 */
export interface ITeacherRankingResponse {
  /** 教师排名列表 */
  list: ITeacherRanking[];
  /** 总记录数 */
  total: number;
}

// ==================== 教师详情相关 ====================

/**
 * 教师统计数据接口
 * @description 教师维度的详细统计分析数据
 * @api GET /api/statistics/teacher
 */
export interface ITeacherStatistics {
  /** SSO教师ID */
  sso_teacher_id: string;
  /** SSO教师姓名 */
  sso_teacher_name: string;
  /** SSO教师科目 */
  sso_teacher_subject: string;
  /** SSO教师部门 */
  sso_teacher_department: string;
  /** 统计月份，格式：YYYY-MM */
  month: string;
  /** 总评价数 */
  total_evaluations: number;
  /** 平均分（百分制，0-100） */
  average_score: number;
  /** 推荐率（百分比，0-100） */
  recommendation_rate: number;
  /** 详细评分数据 */
  detailed_scores: IDetailedScores;
}

/**
 * 详细评分接口
 * @description 教师各维度的详细评分
 */
export interface IDetailedScores {
  /** 教学质量评分（百分制，0-100） */
  teaching_quality: number;
  /** 教学态度评分（百分制，0-100） */
  teaching_attitude: number;
  /** 课堂管理评分（百分制，0-100） */
  classroom_management: number;
  /** 沟通能力评分（百分制，0-100） */
  communication: number;
  /** 专业知识评分（百分制，0-100） */
  professional_knowledge: number;
}

/**
 * 评分分布接口
 * @description 评分区间的分布统计
 * @api GET /api/statistics/teacher/:teacherId/distribution
 */
export interface IScoreDistribution {
  /** 分数区间（如：90-100、80-89等） */
  score_range: string;
  /** 该区间的数量 */
  count: number;
  /** 该区间的占比（百分比，0-100） */
  percentage: number;
}

/**
 * 关键词数据接口
 * @description 关键词云的词汇统计
 * @api GET /api/statistics/teacher/:teacherId/keywords
 */
export interface IKeywordData {
  /** 关键词 */
  word: string;
  /** 出现次数 */
  count: number;
  /** 权重（可选，用于词云大小） */
  weight?: number;
}

// ==================== API 响应结构 ====================

/**
 * 通用API响应结构
 * @description 所有统计接口的统一响应格式
 */
export interface IApiResponse<T = any> {
  /** 错误码，0表示成功 */
  errCode: number;
  /** 响应消息 */
  message: string;
  /** 响应数据 */
  data: T;
  /** 时间戳 */
  timestamp: number;
}

// ==================== 接口路径和说明 ====================

/**
 * 统计接口路径枚举
 * @description 后台需要实现的统计接口路径
 */
export enum StatisticsApiPaths {
  /** 学校统计概览 */
  SCHOOL_STATISTICS = '/api/statistics/school',
  /** 教师排名 */
  TEACHER_RANKING = '/api/statistics/teacher-ranking',
  /** 教师统计概览 */
  TEACHER_STATISTICS = '/api/statistics/teacher',
  /** 教师评分分布 */
  TEACHER_SCORE_DISTRIBUTION = '/api/statistics/teacher/:teacherId/distribution',
  /** 教师关键词云 */
  TEACHER_KEYWORDS = '/api/statistics/teacher/:teacherId/keywords',
}

/**
 * 接口实现说明
 * @description 为后台开发提供的接口实现指南
 */
export const API_IMPLEMENTATION_GUIDE = {
  [StatisticsApiPaths.SCHOOL_STATISTICS]: {
    method: 'GET',
    description: '获取学校统计概览数据',
    queryParams: 'IStatisticsQuery',
    responseType: 'IApiResponse<ISchoolStatistics>',
    notes: '返回指定学校的整体统计信息，包括完成率、平均分等',
    example: {
      request: '?sso_school_code=school001&month=2024-01',
      response: {
        errCode: 0,
        message: 'success',
        data: {
          sso_school_code: 'school001',
          sso_school_name: '示例小学',
          month: '2024-01',
          total_responses: 150,
          completed_responses: 135,
          completion_rate: 90.0,
          school_average_score: 88.5,
          teacher_average_score: 87.2,
          total_teachers_evaluated: 25,
        },
        timestamp: 1640995200000,
      },
    },
  },
  [StatisticsApiPaths.TEACHER_RANKING]: {
    method: 'GET',
    description: '获取教师排名列表',
    queryParams: 'IStatisticsQuery',
    responseType: 'IApiResponse<ITeacherRankingResponse>',
    notes: '返回教师评分排名，支持分页和筛选',
    example: {
      request: '?sso_school_code=school001&page=1&limit=10',
      response: {
        errCode: 0,
        message: 'success',
        data: {
          list: [
            {
              sso_teacher_id: 'teacher001',
              sso_teacher_name: '张老师',
              sso_teacher_subject: '数学',
              sso_teacher_department: '小学部',
              average_score: 95.5,
              evaluation_count: 30,
              recommendation_rate: 96.7,
              rank: 1,
            },
          ],
          total: 25,
        },
        timestamp: 1640995200000,
      },
    },
  },
  [StatisticsApiPaths.TEACHER_STATISTICS]: {
    method: 'GET',
    description: '获取教师详细统计数据',
    queryParams: 'IStatisticsQuery (必须包含 sso_teacher_id)',
    responseType: 'IApiResponse<ITeacherStatistics>',
    notes: '返回指定教师的详细统计信息',
    example: {
      request: '?sso_teacher_id=teacher001&month=2024-01',
      response: {
        errCode: 0,
        message: 'success',
        data: {
          sso_teacher_id: 'teacher001',
          sso_teacher_name: '张老师',
          sso_teacher_subject: '数学',
          sso_teacher_department: '小学部',
          month: '2024-01',
          total_evaluations: 30,
          average_score: 95.5,
          recommendation_rate: 96.7,
          detailed_scores: {
            teaching_quality: 96.0,
            teaching_attitude: 95.5,
            classroom_management: 94.8,
            communication: 96.2,
            professional_knowledge: 95.0,
          },
        },
        timestamp: 1640995200000,
      },
    },
  },
  [StatisticsApiPaths.TEACHER_SCORE_DISTRIBUTION]: {
    method: 'GET',
    description: '获取教师评分分布',
    pathParams: 'teacherId: string',
    queryParams: 'IStatisticsQuery',
    responseType: 'IApiResponse<IScoreDistribution[]>',
    notes: '返回指定教师的评分分布数据，用于饼图展示',
    example: {
      request: '/api/statistics/teacher/teacher001/distribution?month=2024-01',
      response: {
        errCode: 0,
        message: 'success',
        data: [
          { score_range: '90-100', count: 25, percentage: 83.3 },
          { score_range: '80-89', count: 4, percentage: 13.3 },
          { score_range: '70-79', count: 1, percentage: 3.3 },
        ],
        timestamp: 1640995200000,
      },
    },
  },
  [StatisticsApiPaths.TEACHER_KEYWORDS]: {
    method: 'GET',
    description: '获取教师关键词云数据',
    pathParams: 'teacherId: string',
    queryParams: 'IStatisticsQuery',
    responseType: 'IApiResponse<IKeywordData[]>',
    notes: '返回指定教师的评价关键词统计，用于词云展示',
    example: {
      request: '/api/statistics/teacher/teacher001/keywords?month=2024-01',
      response: {
        errCode: 0,
        message: 'success',
        data: [
          { word: '认真负责', count: 15, weight: 1.0 },
          { word: '教学生动', count: 12, weight: 0.8 },
          { word: '耐心细致', count: 10, weight: 0.67 },
          { word: '专业水平高', count: 8, weight: 0.53 },
        ],
        timestamp: 1640995200000,
      },
    },
  },
} as const;
