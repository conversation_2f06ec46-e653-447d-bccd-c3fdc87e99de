// @ts-ignore
/* eslint-disable */
import type {
  IApiResponse,
  IKeywordData,
  ISchoolStatistics,
  IScoreDistribution,
  IStatisticsQuery,
  ITeacherRankingResponse,
  ITeacherStatistics,
} from '@/types/statistics';
import { request } from '@umijs/max';

/**
 * 统计服务
 * @description 学校统计、教师统计等操作
 */

/** 学校统计概览 GET /api/statistics/school */
export async function getSchoolStatistics(params?: IStatisticsQuery) {
  return request<IApiResponse<ISchoolStatistics>>('/api/statistics/school', {
    method: 'GET',
    params,
  });
}

/** 教师排名 GET /api/statistics/teacher-ranking */
export async function getTeacherRanking(params?: IStatisticsQuery) {
  return request<IApiResponse<ITeacherRankingResponse>>(
    '/api/statistics/teacher-ranking',
    {
      method: 'GET',
      params,
    },
  );
}

/** 教师统计概览 GET /api/statistics/teacher */
export async function getTeacherStatistics(params?: IStatisticsQuery) {
  return request<IApiResponse<ITeacherStatistics>>('/api/statistics/teacher', {
    method: 'GET',
    params,
  });
}

/** 教师评分分布 GET /api/statistics/teacher/:teacherId/distribution */
export async function getTeacherScoreDistribution(
  teacherId: string,
  params?: IStatisticsQuery,
) {
  return request<IApiResponse<IScoreDistribution[]>>(
    `/api/statistics/teacher/${teacherId}/distribution`,
    {
      method: 'GET',
      params,
    },
  );
}

/** 教师关键词云 GET /api/statistics/teacher/:teacherId/keywords */
export async function getTeacherKeywords(
  teacherId: string,
  params?: IStatisticsQuery,
) {
  return request<IApiResponse<IKeywordData[]>>(
    `/api/statistics/teacher/${teacherId}/keywords`,
    {
      method: 'GET',
      params,
    },
  );
}
