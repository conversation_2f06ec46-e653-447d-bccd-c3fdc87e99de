# 📊 统计分析页面

## 🎯 功能概述

统计分析页面提供了全面的问卷数据分析和可视化展示，帮助管理员深入了解学校和教师的评价情况。

## ✨ 主要功能

### 1. 数据筛选功能

- **学校选择器**：支持选择特定学校或查看全部学校数据
- **月份选择器**：支持选择最近 12 个月的数据
- **实时刷新**：筛选条件变更时自动刷新所有统计数据
- **搜索功能**：支持模糊搜索学校名称

### 2. 学校整体统计卡片

- **学校平均分**：显示学校整体评价平均分
- **参与人数**：显示已完成评价的人数和总响应数
- **完成率**：显示问卷完成率百分比
- **被评教师数**：显示参与评价的教师总数和教师平均分

### 3. 月度评分趋势图

- **折线图展示**：使用 Ant Design Charts 展示月度趋势
- **双线对比**：同时显示学校平均分和教师平均分趋势
- **交互功能**：支持悬停查看详细数据
- **时间滑块**：支持选择时间范围查看

### 4. 学科平均分对比图

- **柱状图展示**：按学科分组显示平均分对比
- **自动排序**：按平均分降序排列
- **数据标签**：显示具体分值和教师数量
- **响应式设计**：自适应不同屏幕尺寸

### 5. 教师评分排行榜

- **排名展示**：显示教师评分排名（前三名有特殊图标）
- **多维度信息**：教师姓名、学科、部门、平均分、评价人数
- **搜索过滤**：支持按教师姓名、学科、部门搜索
- **排序功能**：支持按平均分和评价人数排序
- **分页展示**：支持分页和每页条数设置

### 6. 教师详情模态框

- **基本统计**：平均分、评价总数、推荐率、教师信息
- **评分分布饼图**：显示各分数段的占比情况
- **关键词云**：展示家长评价中的高频关键词
- **交互设计**：点击教师行或详情按钮打开

## 🛠️ 技术实现

### 组件架构

```
src/pages/Statistics/
├── index.tsx                    # 主页面组件
├── index.less                   # 样式文件
├── components/
│   ├── FilterForm.tsx           # 数据筛选表单
│   ├── SchoolOverview.tsx       # 学校整体统计卡片
│   ├── TeacherRanking.tsx       # 教师排行榜
│   └── TeacherDetailModal.tsx   # 教师详情模态框
└── README.md                    # 说明文档
```

### 数据模型

- **状态管理**：使用 umi dva model 进行状态管理
- **数据缓存**：支持学校列表数据缓存
- **错误处理**：完善的错误提示和加载状态
- **类型安全**：完整的 TypeScript 类型定义

### 图表库

- **Ant Design Charts**：基于 G2Plot 的 React 图表库
- **饼图**：评分分布展示
- **词云图**：关键词可视化

## 📱 响应式设计

- **桌面端**：完整功能展示，多列布局
- **平板端**：自适应布局调整
- **移动端**：单列布局，优化触摸操作

## 🎨 UI/UX 特性

- **现代化设计**：遵循 Ant Design 设计规范
- **动画效果**：图表加载和交互动画
- **主题一致性**：与整体系统保持一致
- **无障碍支持**：键盘导航和屏幕阅读器支持

## 🔧 配置说明

### API 接口

- `GET /api/statistics/school` - 获取学校统计数据
- `GET /api/statistics/teacher-ranking` - 获取教师排名
- `GET /api/statistics/teacher` - 获取教师统计数据
- `GET /api/statistics/teacher/:teacherId/distribution` - 获取评分分布
- `GET /api/statistics/teacher/:teacherId/keywords` - 获取关键词数据
- `GET /auth/schools` - 获取学校列表

> 详细的接口实现指南请参考 `docs/backend-statistics-api.md`

### 路由配置

```typescript
{
  name: '统计分析',
  path: '/statistics',
  wrappers: ['@/wrappers/auth'],
  component: './Statistics',
}
```

## 🚀 使用说明

### 1. 访问页面

导航到 `/statistics` 查看统计分析页面

### 2. 筛选数据

- 自动使用当前用户所属学校数据
- 选择月份查看历史数据
- 点击"查询"按钮刷新数据

### 3. 查看统计

- 查看学校整体统计卡片
- 了解完成率和平均分情况

### 4. 教师排名

- 浏览教师评分排行榜
- 使用搜索功能查找特定教师
- 点击教师查看详细信息

### 5. 详细分析

- 查看教师评分分布
- 分析家长评价关键词
- 了解教师综合表现

## 🔄 数据更新

- **实时性**：数据来源于最新的问卷响应
- **权限控制**：自动使用当前用户所属学校，确保数据安全
- **自动刷新**：筛选条件变更时自动更新所有相关数据

## 🎯 后续优化

- [ ] 添加数据导出功能
- [ ] 支持自定义时间范围选择
- [ ] 增加更多图表类型
- [ ] 添加数据对比功能
- [ ] 支持报告生成和打印
